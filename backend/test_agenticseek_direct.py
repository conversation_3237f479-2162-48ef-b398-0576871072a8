#!/usr/bin/env python3
"""
Direct test of AgenticSeek integration
"""
import os
import sys
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, '.')
sys.path.insert(0, 'app/agenticseek')

async def test_agenticseek():
    """Test AgenticSeek integration directly"""
    try:
        print("🚀 Testing AgenticSeek Integration...")
        print(f"📁 Current working directory: {os.getcwd()}")

        # Check if prompt files exist
        agenticseek_path = os.path.join(os.getcwd(), 'app', 'agenticseek')
        prompt_path = os.path.join(agenticseek_path, 'prompts', 'base', 'coder_agent.txt')
        print(f"🔍 Checking prompt path: {prompt_path}")
        print(f"📄 Prompt file exists: {os.path.exists(prompt_path)}")

        # Import AgenticSeek endpoint function
        from app.api.endpoints.agenticseek import initialize_agenticseek

        print("✅ AgenticSeek endpoint imported successfully")

        # Test initialization
        interaction, error = initialize_agenticseek()

        if error:
            print(f"❌ Initialization error: {error}")
            return False

        print("✅ AgenticSeek initialized successfully")
        print(f"📊 Available agents: {len(interaction.agents)}")
        for agent in interaction.agents:
            print(f"   - {agent.type}")

        # Test a simple query
        test_message = "Hola Emma, ¿puedes ayudarme a crear un plan de marketing para una startup de tecnología?"
        print(f"\n💬 Testing query: {test_message}")

        interaction.set_query(test_message)
        success = await interaction.think()

        if success and interaction.last_answer:
            print("✅ Query processed successfully!")
            print(f"🤖 Agent used: {interaction.current_agent.type if interaction.current_agent else 'unknown'}")
            print(f"📝 Response: {interaction.last_answer[:200]}...")
            return True
        else:
            print("❌ Query processing failed")
            return False

    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_agenticseek())
    if success:
        print("\n🎉 AgenticSeek integration test PASSED!")
    else:
        print("\n💥 AgenticSeek integration test FAILED!")
