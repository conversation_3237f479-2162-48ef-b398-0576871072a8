"""
Cloud-native AgenticSeek implementation - NO Docker, NO local dependencies
"""
import os
import json
import requests
import asyncio
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel
import google.generativeai as genai
from datetime import datetime
import tempfile
import mimetypes

router = APIRouter()

# Configure Gemini
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))

class CloudAgenticSeekRequest(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None

class CloudAgenticSeekResponse(BaseModel):
    success: bool
    response: str
    agent_used: str
    thinking_process: Optional[str] = None
    sources: Optional[list] = None
    error: Optional[str] = None

class CloudSearchService:
    """Cloud-based search using Serper API (no Docker needed)"""

    def __init__(self):
        self.api_key = os.getenv("SERPER_API_KEY")
        self.base_url = "https://google.serper.dev"

    async def search(self, query: str, num_results: int = 5) -> Dict[str, Any]:
        """Search the web using Serper API"""
        if not self.api_key:
            raise HTTPException(status_code=500, detail="Serper API key not configured")

        url = f"{self.base_url}/search"
        headers = {
            'X-API-KEY': self.api_key,
            'Content-Type': 'application/json'
        }
        payload = {
            "q": query,
            "num": num_results,
            "gl": "us",
            "hl": "en"
        }

        try:
            response = requests.post(url, headers=headers, json=payload, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

class CloudAgentRouter:
    """Cloud-based agent routing using Gemini (no heavy ML models)"""

    def __init__(self):
        self.model = genai.GenerativeModel('gemini-1.5-flash')

    async def route_request(self, message: str) -> str:
        """Determine which agent should handle the request"""
        prompt = f"""
        Analyze this user request and determine the best agent type:

        Request: "{message}"

        Available agents:
        - planner: For strategic planning, marketing plans, business strategy
        - researcher: For web research, data gathering, fact-checking
        - writer: For content creation, copywriting, articles
        - analyst: For data analysis, insights, recommendations

        Respond with just the agent name (planner/researcher/writer/analyst).
        """

        try:
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            agent_type = response.text.strip().lower()
            return agent_type if agent_type in ['planner', 'researcher', 'writer', 'analyst'] else 'planner'
        except Exception:
            return 'planner'  # Default fallback

class CloudEmmaAgent:
    """Cloud-native Emma agent - pure API calls, no local dependencies"""

    def __init__(self):
        self.search_service = CloudSearchService()
        self.router = CloudAgentRouter()
        self.model = genai.GenerativeModel('gemini-1.5-flash')

    async def process_request(self, message: str, context: Optional[Dict] = None) -> CloudAgenticSeekResponse:
        """Process user request using cloud services only"""
        try:
            # Step 1: Route to appropriate agent
            agent_type = await self.router.route_request(message)

            # Step 2: Determine if web search is needed
            search_needed = await self._needs_web_search(message)

            # Step 3: Perform web search if needed
            search_results = None
            sources = []
            if search_needed:
                try:
                    search_data = await self.search_service.search(message)
                    search_results = self._format_search_results(search_data)
                    sources = self._extract_sources(search_data)
                except Exception as e:
                    # Continue without search if it fails
                    search_results = f"Search unavailable: {str(e)}"

            # Step 4: Generate response using Gemini
            response_text, thinking = await self._generate_response(
                message, agent_type, search_results, context
            )

            return CloudAgenticSeekResponse(
                success=True,
                response=response_text,
                agent_used=f"Emma ({agent_type})",
                thinking_process=thinking,
                sources=sources
            )

        except Exception as e:
            return CloudAgenticSeekResponse(
                success=False,
                response="Lo siento, hubo un error procesando tu solicitud.",
                agent_used="Emma (error)",
                error=str(e)
            )

    async def _needs_web_search(self, message: str) -> bool:
        """Determine if web search is needed using Gemini"""
        prompt = f"""
        Does this request need current web information or real-time data?

        Request: "{message}"

        Answer only "yes" or "no".

        Examples that need web search:
        - Current news, trends, prices
        - Recent events, statistics
        - Company information, contact details
        - Product reviews, comparisons

        Examples that don't need web search:
        - General advice, strategies
        - Creative writing, brainstorming
        - Explanations of concepts
        - Personal opinions, analysis
        """

        try:
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            return "yes" in response.text.lower()
        except Exception:
            return False  # Default to no search if detection fails

    def _format_search_results(self, search_data: Dict) -> str:
        """Format search results for AI processing"""
        results = []

        # Organic results
        for result in search_data.get('organic', [])[:5]:
            results.append(f"Title: {result.get('title', '')}")
            results.append(f"Snippet: {result.get('snippet', '')}")
            results.append(f"URL: {result.get('link', '')}")
            results.append("---")

        # Answer box if available
        if 'answerBox' in search_data:
            answer = search_data['answerBox']
            results.insert(0, f"Featured Answer: {answer.get('answer', '')}")
            results.insert(1, "---")

        return "\n".join(results)

    def _extract_sources(self, search_data: Dict) -> list:
        """Extract source URLs for citation"""
        sources = []
        for result in search_data.get('organic', [])[:3]:
            if 'link' in result and 'title' in result:
                sources.append({
                    'title': result['title'],
                    'url': result['link']
                })
        return sources

    async def _generate_response(self, message: str, agent_type: str, search_results: Optional[str], context: Optional[Dict]) -> tuple:
        """Generate response using Gemini with agent personality"""

        agent_prompts = {
            'planner': "Eres Emma, una experta en planificación estratégica y marketing. Creas planes detallados y estrategias efectivas.",
            'researcher': "Eres Emma, una investigadora experta. Analizas información y proporcionas insights basados en datos.",
            'writer': "Eres Emma, una escritora y copywriter experta. Creas contenido persuasivo y atractivo.",
            'analyst': "Eres Emma, una analista experta. Interpretas datos y proporcionas recomendaciones estratégicas."
        }

        system_prompt = agent_prompts.get(agent_type, agent_prompts['planner'])

        prompt = f"""
        {system_prompt}

        Solicitud del usuario: "{message}"

        {f"Información de búsqueda web: {search_results}" if search_results else ""}

        {f"Contexto adicional: {json.dumps(context, indent=2)}" if context else ""}

        Instrucciones:
        1. Responde de manera profesional y útil
        2. Si usaste información web, menciónalo
        3. Proporciona respuestas específicas y accionables
        4. Mantén un tono amigable pero profesional
        5. Responde en español

        Proporciona tu respuesta y después, en una línea separada que comience con "THINKING:", explica brevemente tu proceso de razonamiento.
        """

        try:
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            full_response = response.text

            # Split response and thinking
            if "THINKING:" in full_response:
                parts = full_response.split("THINKING:", 1)
                response_text = parts[0].strip()
                thinking = parts[1].strip() if len(parts) > 1 else ""
            else:
                response_text = full_response
                thinking = f"Procesé tu solicitud como {agent_type} y generé una respuesta personalizada."

            return response_text, thinking

        except Exception as e:
            return f"Lo siento, hubo un error generando la respuesta: {str(e)}", ""

# Initialize cloud agent
cloud_emma = CloudEmmaAgent()

@router.post("/agenticseek-cloud/chat", response_model=CloudAgenticSeekResponse)
async def cloud_agenticseek_chat(request: CloudAgenticSeekRequest):
    """
    Cloud-native AgenticSeek endpoint - NO Docker dependencies!
    """
    return await cloud_emma.process_request(request.message, request.context)

@router.get("/agenticseek-cloud/status")
async def cloud_agenticseek_status():
    """Check cloud AgenticSeek status"""
    return {
        "status": "online",
        "mode": "cloud-native",
        "dependencies": {
            "docker": False,
            "local_models": False,
            "search_service": "serper_api",
            "ai_service": "gemini_api"
        },
        "ready": True
    }
