import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import axios from 'axios';
import './App.css';

function App() {
    const [query, setQuery] = useState('');
    const [messages, setMessages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [currentView, setCurrentView] = useState('blocks');
    const [responseData, setResponseData] = useState(null);
    const [isOnline, setIsOnline] = useState(false);
    const [status, setStatus] = useState('Agents ready');
    const [expandedReasoning, setExpandedReasoning] = useState(new Set());
  const [searchActivity, setSearchActivity] = useState([]);
  const [currentSearch, setCurrentSearch] = useState(null);
  const [browserMode, setBrowserMode] = useState(false); // false = cloud, true = browser
  const [screenshotUrl, setScreenshotUrl] = useState(null);
    const messagesEndRef = useRef(null);

    useEffect(() => {
        const intervalId = setInterval(() => {
            checkHealth();
            if (browserMode) {
                fetchScreenshot(); // Fetch screenshots only in browser mode
            }
        }, 3000);
        return () => clearInterval(intervalId);
    }, [messages, browserMode]);

    const checkHealth = async () => {
        try {
            await axios.get('http://127.0.0.1:8000/api/v1/agenticseek-cloud/status');
            setIsOnline(true);
            console.log('Cloud AgenticSeek is online');
        } catch {
            setIsOnline(false);
            console.log('Cloud AgenticSeek is offline');
        }
    };

    const fetchScreenshot = async () => {
        if (!browserMode) return; // Only fetch screenshots in browser mode

        try {
            const timestamp = new Date().getTime();
            const res = await axios.get(`http://127.0.0.1:8000/api/v1/agenticseek/screenshot?timestamp=${timestamp}`, {
                responseType: 'blob'
            });

            if (screenshotUrl) {
                URL.revokeObjectURL(screenshotUrl);
            }

            const imageUrl = URL.createObjectURL(res.data);
            setScreenshotUrl(imageUrl);
            console.log('Screenshot updated');
        } catch (err) {
            console.log('No screenshot available yet');
            setScreenshotUrl(null);

            // Add search activity when browser mode is active
            if (browserMode && isLoading) {
                setSearchActivity(prev => [...prev.slice(-4), {
                    id: Date.now(),
                    type: 'browser',
                    content: 'Navegador en uso - Esperando captura de pantalla...',
                    timestamp: new Date().toLocaleTimeString()
                }]);
            }
        }
    };

    const normalizeAnswer = (answer) => {
        return answer
            .trim()
            .toLowerCase()
            .replace(/\s+/g, ' ')
            .replace(/[.,!?]/g, '')
    };

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    const toggleReasoning = (messageIndex) => {
        setExpandedReasoning(prev => {
            const newSet = new Set(prev);
            if (newSet.has(messageIndex)) {
                newSet.delete(messageIndex);
            } else {
                newSet.add(messageIndex);
            }
            return newSet;
        });
    };

    const fetchLatestAnswer = async () => {
        try {
            const res = await axios.get('http://127.0.0.1:8000/latest_answer');
            const data = res.data;

            updateData(data);
            if (!data.answer || data.answer.trim() === '') {
                return;
            }
            const normalizedNewAnswer = normalizeAnswer(data.answer);
            const answerExists = messages.some(
                (msg) => normalizeAnswer(msg.content) === normalizedNewAnswer
            );
            if (!answerExists) {
                setMessages((prev) => [
                    ...prev,
                    {
                        type: 'agent',
                        content: data.answer,
                        reasoning: data.reasoning,
                        agentName: data.agent_name,
                        status: data.status,
                        uid: data.uid,
                    },
                ]);
                setStatus(data.status);
                scrollToBottom();
            } else {
                console.log('Duplicate answer detected, skipping:', data.answer);
            }
        } catch (error) {
            console.error('Error fetching latest answer:', error);
        }
    };

    const updateData = (data) => {
        setResponseData((prev) => ({
            ...prev,
            blocks: data.blocks || (prev && prev.blocks) || null,
            done: data.done,
            answer: data.answer,
            agent_name: data.agent_name,
            status: data.status,
            uid: data.uid,
        }));
    };

    const handleStop = async (e) => {
        e.preventDefault();
        checkHealth();
        setIsLoading(false);
        setError(null);
        setStatus("Stopped by user");
        // Stop functionality disabled for Emma integration
    }

    const handleSubmit = async (e) => {
        e.preventDefault();
        checkHealth();
        if (!query.trim()) {
            console.log('Empty query');
            return;
        }
        setMessages((prev) => [...prev, { type: 'user', content: query }]);
        setIsLoading(true);
        setError(null);

        try {
            console.log('Sending query:', query);
            setQuery('waiting for response...');

            // Simulate search activity
            setCurrentSearch(`Analizando: "${query}"`);
            setSearchActivity(prev => [...prev, {
                id: Date.now(),
                type: 'query',
                content: `Nueva consulta: ${query}`,
                timestamp: new Date().toLocaleTimeString()
            }]);

            // Add search simulation
            setTimeout(() => {
                setCurrentSearch('🔍 Buscando información relevante...');
                setSearchActivity(prev => [...prev, {
                    id: Date.now() + 1,
                    type: 'search',
                    content: 'Iniciando búsqueda en internet',
                    timestamp: new Date().toLocaleTimeString()
                }]);
            }, 500);

            // Choose endpoint based on mode
            const endpoint = browserMode
                ? 'http://127.0.0.1:8000/api/v1/agenticseek-browser/chat'
                : 'http://127.0.0.1:8000/api/v1/agenticseek-cloud/chat';

            const res = await axios.post(endpoint, {
                message: query
            });
            setQuery('Enter your query...');
            console.log('Response:', res.data);

            // Handle cloud AgenticSeek response format
            if (res.data.success) {
                const data = {
                    answer: res.data.response,
                    agent_name: res.data.agent_used || 'Emma',
                    reasoning: res.data.thinking_process || '',
                    status: 'Completed',
                    uid: Date.now().toString(),
                    sources: res.data.sources || []
                };
                updateData(data);

                // Add agent response to messages
                setMessages((prev) => [
                    ...prev,
                    {
                        type: 'agent',
                        content: res.data.response,
                        agentName: res.data.agent_used || 'Emma',
                        reasoning: res.data.thinking_process || '',
                        sources: res.data.sources || []
                    }
                ]);
            } else {
                // Handle error response
                setError(res.data.error || 'Unknown error occurred');
                setMessages((prev) => [
                    ...prev,
                    { type: 'error', content: `Error: ${res.data.error || 'Unknown error occurred'}` }
                ]);
            }
        } catch (err) {
            console.error('Error:', err);
            setError('Failed to process query.');
            setMessages((prev) => [
                ...prev,
                { type: 'error', content: 'Error: Unable to get a response.' },
            ]);
        } finally {
            console.log('Query completed');
            setIsLoading(false);
            setQuery('');
        }
    };

    // Screenshot functionality disabled for cloud-native version

    return (
        <div className="app">
            <header className="header">
                <div className="header-content">
                    <div className="emma-branding">
                        <img src="/emma-profile.png" alt="Emma AI" className="emma-avatar" />
                        <div className="emma-info">
                            <h1>Emma AI</h1>
                            <p>Tu asistente de IA para marketing</p>
                        </div>
                    </div>
                    <div className="status-badge">
                        <span className={`status-dot ${isOnline ? 'online' : 'offline'}`}></span>
                        {isOnline ? 'En línea' : 'Desconectado'}
                    </div>
                </div>
            </header>
            <main className="main">
                <div className="mode-selector">
                    <button
                        className={`mode-btn ${!browserMode ? 'active' : ''}`}
                        onClick={() => setBrowserMode(false)}
                    >
                        🚀 Cloud Mode (Rápido)
                    </button>
                    <button
                        className={`mode-btn ${browserMode ? 'active' : ''}`}
                        onClick={() => setBrowserMode(true)}
                    >
                        🌐 Browser Mode (Screenshots)
                    </button>
                </div>

                <div className="app-sections">
                    <div className="chat-section">
                        <h2>Chat Interface</h2>
                        <div className="messages">
                            {messages.length === 0 ? (
                                <div className="welcome-message">
                                    <img src="/emma-profile.png" alt="Emma AI" className="welcome-avatar" />
                                    <div className="welcome-content">
                                        <h3>¡Hola! Soy Emma 👋</h3>
                                        <p>Tu asistente de IA especializada en marketing. Puedo ayudarte con:</p>
                                        <ul>
                                            <li>🔍 Investigación de mercado</li>
                                            <li>📊 Análisis de competencia</li>
                                            <li>💡 Estrategias de contenido</li>
                                            <li>🎯 Campañas publicitarias</li>
                                        </ul>
                                        <p className="welcome-cta">¿En qué puedo ayudarte hoy?</p>
                                    </div>
                                </div>
                            ) : (
                                messages.map((msg, index) => (
                                    <div
                                        key={index}
                                        className={`message ${
                                            msg.type === 'user'
                                                ? 'user-message'
                                                : msg.type === 'agent'
                                                ? 'agent-message'
                                                : 'error-message'
                                        }`}
                                    >
                                        <div className="message-header">
                                            {msg.type === 'agent' && (
                                                <div className="agent-header">
                                                    <img src="/emma-profile.png" alt="Emma AI" className="message-avatar" />
                                                    <span className="agent-name">{msg.agentName || 'Emma'}</span>
                                                </div>
                                            )}
                                            {msg.type === 'agent' && msg.reasoning && expandedReasoning.has(index) && (
                                                <div className="reasoning-content">
                                                    <ReactMarkdown>{msg.reasoning}</ReactMarkdown>
                                                </div>
                                            )}
                                            {msg.type === 'agent' && (
                                                <button
                                                    className="reasoning-toggle"
                                                    onClick={() => toggleReasoning(index)}
                                                    title={expandedReasoning.has(index) ? "Hide reasoning" : "Show reasoning"}
                                                >
                                                    {expandedReasoning.has(index) ? '▼' : '▶'} Reasoning
                                                </button>
                                            )}
                                        </div>
                                        <div className="message-content">
                                            <ReactMarkdown>{msg.content}</ReactMarkdown>
                                            {msg.sources && msg.sources.length > 0 && (
                                                <div className="sources">
                                                    <h4>Sources:</h4>
                                                    <ul>
                                                        {msg.sources.map((source, idx) => (
                                                            <li key={idx}>
                                                                <a href={source.url} target="_blank" rel="noopener noreferrer">
                                                                    {source.title}
                                                                </a>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                ))
                            )}
                            <div ref={messagesEndRef} />
                        </div>
                        {isOnline && <div className="loading-animation">{status}</div>}
                        {!isLoading && !isOnline && <p className="loading-animation">Cloud AgenticSeek offline. Check backend connection.</p>}
                        <form onSubmit={handleSubmit} className="input-form">
                            <div className="input-container">
                                <input
                                    type="text"
                                    value={query}
                                    onChange={(e) => setQuery(e.target.value)}
                                    placeholder="Escribe tu consulta aquí..."
                                    disabled={isLoading}
                                />
                                <div className="input-actions">
                                    <label className="file-upload-btn" title="Subir archivo">
                                        📎
                                        <input
                                            type="file"
                                            onChange={handleFileUpload}
                                            accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.csv,.xlsx,.xls"
                                            style={{display: 'none'}}
                                        />
                                    </label>
                                    <button type="submit" disabled={isLoading}>
                                        Enviar
                                    </button>
                                    <button type="button" onClick={handleStop}>
                                        Parar
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div className="computer-section">
                        <h2>Computer View</h2>
                        <div className="view-selector">
                            <button
                                className={currentView === 'blocks' ? 'active' : ''}
                                onClick={() => setCurrentView('blocks')}
                            >
                                🔄 Proceso de Trabajo
                            </button>
                            <button
                                className={currentView === 'screenshot' ? 'active' : ''}
                                onClick={() => setCurrentView('screenshot')}
                            >
                                📊 Estado de Emma
                            </button>
                        </div>
                        <div className="content">
                            {error && <p className="error">{error}</p>}
                            {currentView === 'blocks' ? (
                                <div className="workflow-view">
                                    {responseData && responseData.blocks && Object.values(responseData.blocks).length > 0 ? (
                                        <div className="workflow-active">
                                            <h4>🔄 Emma está trabajando</h4>
                                            <div className="workflow-steps">
                                                {Object.values(responseData.blocks).map((block, index) => (
                                                    <div key={index} className="workflow-step">
                                                        <div className="step-indicator">
                                                            {block.success ? '✅' : '⏳'}
                                                        </div>
                                                        <div className="step-content">
                                                            <p className="step-title">
                                                                {block.tool_type === 'search' ? 'Buscando información' :
                                                                 block.tool_type === 'browser' ? 'Navegando web' :
                                                                 block.tool_type === 'analysis' ? 'Analizando datos' :
                                                                 'Procesando solicitud'}
                                                            </p>
                                                            {block.success && (
                                                                <p className="step-status">Completado</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="workflow-idle">
                                            <div className="idle-icon">💤</div>
                                            <h4>Emma está lista</h4>
                                            <p>Haz una pregunta para ver el proceso de trabajo de Emma</p>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="browser-view">
                                    {browserMode ? (
                                        <div className="screenshot-container">
                                            <div className="screenshot-header">
                                                <h3>🌐 Emma's Browser View</h3>
                                                <div className={`status-indicator ${isOnline ? 'online' : 'offline'}`}>
                                                    {isOnline ? '🟢 Online' : '🔴 Offline'}
                                                </div>
                                            </div>

                                            {screenshotUrl ? (
                                                <div className="screenshot">
                                                    <img
                                                        src={screenshotUrl}
                                                        alt="Emma's Browser Screenshot"
                                                        style={{maxWidth: '100%', border: '1px solid #4a5568', borderRadius: '8px'}}
                                                    />
                                                </div>
                                            ) : (
                                                <div className="browser-activity">
                                                    <div className="activity-header">
                                                        <h4>🌐 Actividad del Navegador</h4>
                                                        {isLoading && (
                                                            <div className="loading-spinner">
                                                                <div className="spinner"></div>
                                                                <span>Emma está navegando...</span>
                                                            </div>
                                                        )}
                                                    </div>

                                                    <div className="activity-log">
                                                        {searchActivity.length > 0 ? (
                                                            searchActivity.slice(-5).map(activity => (
                                                                <div key={activity.id} className={`activity-item ${activity.type}`}>
                                                                    <span className="activity-time">{activity.timestamp}</span>
                                                                    <span className="activity-content">{activity.content}</span>
                                                                </div>
                                                            ))
                                                        ) : (
                                                            <div className="no-activity">
                                                                <p>🔍 Esperando que Emma use el navegador...</p>
                                                                <p>En modo navegador, Emma puede:</p>
                                                                <ul>
                                                                    <li>• Navegar por sitios web</li>
                                                                    <li>• Buscar información específica</li>
                                                                    <li>• Tomar capturas de pantalla</li>
                                                                    <li>• Interactuar con páginas web</li>
                                                                </ul>
                                                            </div>
                                                        )}
                                                    </div>

                                                    {currentSearch && (
                                                        <div className="current-search">
                                                            <h5>🔍 Búsqueda Actual:</h5>
                                                            <p>{currentSearch}</p>
                                                        </div>
                                                    )}
                                                </div>
                                            )}

                                            <div className="browser-benefits">
                                                <h4>🌐 Modo Navegador Activo</h4>
                                                <div className="benefits-list">
                                                    <div className="benefit-item">
                                                        <span className="benefit-icon">📸</span>
                                                        <span className="benefit-text">Capturas de pantalla en tiempo real</span>
                                                    </div>
                                                    <div className="benefit-item">
                                                        <span className="benefit-icon">🔍</span>
                                                        <span className="benefit-text">Búsqueda web avanzada</span>
                                                    </div>
                                                    <div className="benefit-item">
                                                        <span className="benefit-icon">🎯</span>
                                                        <span className="benefit-text">Análisis visual de sitios web</span>
                                                    </div>
                                                    <div className="benefit-item">
                                                        <span className="benefit-icon">⚡</span>
                                                        <span className="benefit-text">Investigación automática</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="agent-status">
                                            <div className="status-header">
                                                <h3>🤖 Emma AI Status</h3>
                                                <div className={`status-indicator ${isOnline ? 'online' : 'offline'}`}>
                                                    {isOnline ? '🟢 Online' : '🔴 Offline'}
                                                </div>
                                            </div>

                                            <div className="current-activity">
                                                <h4>📋 Current Activity</h4>
                                                <p className="activity-text">{status}</p>
                                                {isLoading && (
                                                    <div className="loading-spinner">
                                                        <div className="spinner"></div>
                                                        <span>Emma está procesando tu solicitud...</span>
                                                    </div>
                                                )}
                                            </div>

                                            <div className="agent-capabilities">
                                                <h4>✨ Capacidades de Emma</h4>
                                                <div className="capabilities-list">
                                                    <div className="capability-item">
                                                        <span className="capability-icon">🔍</span>
                                                        <span className="capability-text">Investigación de mercado avanzada</span>
                                                    </div>
                                                    <div className="capability-item">
                                                        <span className="capability-icon">📊</span>
                                                        <span className="capability-text">Análisis de competencia</span>
                                                    </div>
                                                    <div className="capability-item">
                                                        <span className="capability-icon">🎯</span>
                                                        <span className="capability-text">Estrategias de contenido</span>
                                                    </div>
                                                    <div className="capability-item">
                                                        <span className="capability-icon">🌐</span>
                                                        <span className="capability-text">Navegación web inteligente</span>
                                                    </div>
                                                </div>
                                            </div>

                                            {responseData && responseData.agent_name && (
                                                <div className="recent-activity">
                                                    <h4>📈 Actividad Reciente</h4>
                                                    <div className="activity-summary">
                                                        <p><strong>Última consulta procesada:</strong> {new Date().toLocaleTimeString()}</p>
                                                        <p><strong>Estado:</strong> Completada exitosamente</p>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
}

export default App;