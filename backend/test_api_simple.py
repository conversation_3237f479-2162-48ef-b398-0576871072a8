#!/usr/bin/env python3
"""
Test script simple para probar la API de AgenticSeek con CloudBrowser
"""

import sys
import os
import configparser
from fastapi import FastAPI
from fastapi.responses import FileResponse, JSONResponse

# Agregar el path de agenticseek
sys.path.append(os.path.join(os.path.dirname(__file__), 'app', 'agenticseek'))

from app.agenticseek.sources.browser import CloudBrowser, create_cloud_browser
from app.agenticseek.sources.logger import Logger

# Configuración simple
config = configparser.ConfigParser()
config.read_string("""
[MAIN]
is_local = False
provider_name = google
provider_model = gemini-1.5-flash
provider_server_address = generativelanguage.googleapis.com
agent_name = Emma
recover_last_session = False
save_session = False
speak = False
listen = False
work_dir = /tmp/emma_workspace
jarvis_personality = False
languages = en es

[BROWSER]
headless_browser = True
stealth_mode = True
use_cloud_browser = True
browserless_api_key = 2SP6LRG5ebh7ohfc3b60651a5f2f574bc95ee4e3c8caf2a58

[SEARCH]
use_external_search = True
search_provider = serper
fallback_to_builtin = False
""")

app = FastAPI(title="Emma CloudBrowser Test API", version="0.1.0")
logger = Logger("test_api.log")

# Crear directorio de screenshots
if not os.path.exists(".screenshots"):
    os.makedirs(".screenshots")

# Inicializar CloudBrowser
browser = None

def initialize_browser():
    """Inicializar el navegador cloud"""
    global browser
    try:
        use_cloud_browser = config.getboolean('BROWSER', 'use_cloud_browser')
        if use_cloud_browser:
            browserless_api_key = config.get('BROWSER', 'browserless_api_key')
            browser = create_cloud_browser(browserless_api_key)
            logger.info("CloudBrowser initialized successfully")
        else:
            logger.error("Cloud browser not enabled in config")
    except Exception as e:
        logger.error(f"Browser initialization failed: {str(e)}")
        raise

# Inicializar al arrancar
initialize_browser()

@app.get("/")
async def root():
    return {"message": "Emma CloudBrowser Test API", "status": "running"}

@app.get("/screenshot")
async def get_screenshot():
    """Endpoint para obtener screenshot"""
    logger.info("Screenshot endpoint called")
    screenshot_path = ".screenshots/updated_screen.png"
    if os.path.exists(screenshot_path):
        return FileResponse(screenshot_path)
    logger.error("No screenshot available")
    return JSONResponse(
        status_code=404,
        content={"error": "No screenshot available"}
    )

@app.get("/navigate/{url:path}")
async def navigate_to_url(url: str):
    """Endpoint para navegar a una URL"""
    global browser
    if not browser:
        return JSONResponse(
            status_code=500,
            content={"error": "Browser not initialized"}
        )
    
    try:
        logger.info(f"Navigating to: {url}")
        
        # Agregar protocolo si no lo tiene
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            
        success = browser.go_to(url)
        
        if success:
            title = browser.get_page_title()
            text_preview = browser.get_text()
            text_preview = text_preview[:500] + "..." if text_preview and len(text_preview) > 500 else text_preview
            
            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "url": url,
                    "title": title,
                    "text_preview": text_preview,
                    "screenshot_available": os.path.exists(browser.get_screenshot())
                }
            )
        else:
            return JSONResponse(
                status_code=400,
                content={"error": f"Failed to navigate to {url}"}
            )
            
    except Exception as e:
        logger.error(f"Error navigating to {url}: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "browser_initialized": browser is not None,
        "screenshot_folder_exists": os.path.exists(".screenshots")
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Emma CloudBrowser Test API...")
    print("📱 CloudBrowser initialized with Browserless.io")
    print("🌐 API running on http://localhost:8001")
    print("📸 Screenshot endpoint: http://localhost:8001/screenshot")
    print("🔗 Navigate endpoint: http://localhost:8001/navigate/google.com")
    uvicorn.run(app, host="0.0.0.0", port=8001)
