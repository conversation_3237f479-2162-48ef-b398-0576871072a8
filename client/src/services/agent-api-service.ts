import api from "@/lib/api";
import { getApiBaseUrl } from "./detect-deployment";

// API base URL - using consistent path format
const API_BASE = '/api';
console.log('API_BASE initialized as:', API_BASE);

// Define a type for the expected error response structure
interface ErrorResponse {
  error?: {
    code?: string;
    message?: string;
    request_id?: string;
    trace_snippet?: string[];
  };
}

// Types for crew operations
export interface AgentChatRequest {
  agent_id: string;
  message: string;
  context?: Record<string, unknown>;
}

export interface AgentChatResponse {
  response: string;
  reasoning_trace?: ReasoningTraceItem[];
  metadata?: Record<string, unknown>;
  error?: string;
}

// New Relevance AI style events
export interface DelegationEvent {
  id: string;
  agent_from: string;
  agent_to: string;
  task_description: string;
  status: 'started' | 'in_progress' | 'completed' | 'failed';
  started_at: number;
  completed_at?: number;
}

export interface ConversationMessage {
  id: string;
  from: string;
  to: string;
  content: string;
  timestamp: number;
}

export interface StreamEvent {
  type: 'delegation_start' | 'delegation_progress' | 'delegation_complete' | 'conversation_update' | 'final_response' | 'stream_end' | 'error';

  // For delegation events
  delegation?: DelegationEvent;

  // For conversation events
  conversation_id?: string;
  message?: ConversationMessage;

  // For final response
  response?: string;

  // For errors
  error_message?: string;

  timestamp: number;
}

export interface CrewRunRequest {
  /** Unique identifier for the crew run */
  crew_id: string;

  /** The prompt to process */
  prompt: string;

  /** Input parameters for the crew execution */
  inputs?: Record<string, unknown>;

  /** Optional context for the crew run */
  context?: Record<string, unknown>;

  /** Optional configuration for the crew run */
  config?: Record<string, unknown>;
}

export interface ReasoningTraceItem {
  type: "prompt" | "asset" | "error" | "info";
  content: string;
  agent?: string;
  timestamp: string;
  trace_snippet?: string[];
}

export interface CrewRunResponse {
  result: string;
  reasoning_trace: ReasoningTraceItem[];
  status?: string;
}

/**
 * Service for interacting with the Agent API endpoints
 */
export class AgentApiService {
  /**
   * Start a new agent chat session
   * @param request Chat request with agent_id and message
   * @returns Chat response with result and messages
   */
  async startChat(request: AgentChatRequest): Promise<AgentChatResponse> {
    try {
      // Ensure the request has the required fields
      if (!request.agent_id) {
        throw new Error("Agent ID is required for chat");
      }
      if (!request.message) {
        throw new Error("Message is required for chat");
      }

      // Format the request to match the backend API
      const formattedRequest: AgentChatRequest = {
        agent_id: request.agent_id,
        message: request.message,
        context: request.context || {}
      };

      // Log the request for debugging
      console.log('Sending chat request to:', `${API_BASE}/v1/crew/chat`);
      console.log('Request payload:', JSON.stringify(formattedRequest, null, 2));

      // Call the backend API with consistent path and add timeout
      const { data } = await api.post(`${API_BASE}/v1/crew/chat`, formattedRequest, {
        timeout: 30000, // 30 second timeout
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': `req_${Date.now()}`
        }
      });

      // Log the response for debugging
      console.log('Received chat response:', JSON.stringify(data, null, 2));

      // Validate the response
      if (!data || !data.response) {
        console.warn('Received empty or invalid response from API:', data);
        return {
          response: "I received an empty response. Please try again or contact support if the issue persists.",
          metadata: { error: "empty_response" }
        };
      }

      return data;
    } catch (error: unknown) {
      console.error("Error starting agent chat:", error);

      const errorMessage = error instanceof Error
        ? error.message
        : "Unknown error";

      let errorCode = "unknown_error";
      let requestId = "unknown";

      if (error && typeof error === 'object' && 'response' in error &&
          error.response && typeof error.response === 'object' && 'data' in error.response) {
        const responseData = error.response.data as ErrorResponse;
        errorCode = responseData?.error?.code || errorCode;
        requestId = responseData?.error?.request_id || requestId;
      }

      throw new Error(`Agent chat failed (${errorCode}): ${errorMessage} [Request ID: ${requestId}]`);
    }
  }

  /**
   * Start a streaming chat session - RELEVANCE AI STYLE
   * Shows the team working in real-time
   */
  async startStreamingChat(
    request: AgentChatRequest,
    onEvent: (event: StreamEvent) => void
  ): Promise<void> {
    try {
      // Ensure the request has the required fields
      if (!request.agent_id) {
        throw new Error("Agent ID is required for chat");
      }
      if (!request.message) {
        throw new Error("Message is required for chat");
      }

      const formattedRequest: AgentChatRequest = {
        agent_id: request.agent_id,
        message: request.message,
        context: request.context || {}
      };

      console.log('Starting streaming chat to:', `${API_BASE}/v1/crew/chat/stream`);

      // Use fetch for streaming instead of axios - Fixed to use API_BASE instead of getApiBaseUrl()
      const response = await fetch(`${API_BASE}/v1/crew/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': `req_${Date.now()}`
        },
        body: JSON.stringify(formattedRequest)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          buffer += decoder.decode(value, { stream: true });

          // Process complete lines
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const eventData = JSON.parse(line.slice(6));
                onEvent(eventData);
              } catch (e) {
                console.warn('Failed to parse SSE data:', line, e);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

    } catch (error: unknown) {
      console.error("Error in streaming chat:", error);

      const errorMessage = error instanceof Error ? error.message : "Unknown error";

      onEvent({
        type: 'error',
        error_message: `Streaming chat failed: ${errorMessage}`,
        timestamp: Date.now()
      });
    }
  }

  // This endpoint doesn't exist in the backend
  // /**
  //  * Get messages for a specific session
  //  * @param sessionId The session ID
  //  * @returns Array of messages
  //  */
  // async getMessages(sessionId: string): Promise<AgentChatResponse> {
  //   try {
  //     const { data } = await api.get(`${API_BASE}/v1/agent/${sessionId}/messages`);
  //     return data;
  //   } catch (error: unknown) {
  //     console.error(`Error fetching messages for session ${sessionId}:`, error);
  //     throw new Error(`Failed to fetch messages: ${error instanceof Error ? error.message : 'Unknown error'}`);
  //   }
  // }

  /**
   * Run an agent workflow with a specific prompt
   * @param request Agent workflow request
   * @param apiKey Optional API key for authentication
   * @returns Agent workflow response with result and reasoning trace
   */
  async runWorkflow(request: CrewRunRequest, apiKey?: string): Promise<CrewRunResponse> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      if (apiKey) {
        headers['X-API-Key'] = apiKey;
      }

      // Ensure the request has the required fields
      if (!request.prompt) {
        throw new Error("Prompt is required for crew run");
      }

      // Format the request to match the backend API
      const formattedRequest: CrewRunRequest = {
        crew_id: request.crew_id,
        prompt: request.prompt,
        inputs: request.inputs || {},
        context: request.context || {},
        config: request.config || {}
      };

      // Call the backend API with consistent path
      console.log('Sending crew run request to:', `${API_BASE}/v1/crew/run`);
      const { data } = await api.post(`${API_BASE}/v1/crew/run`, formattedRequest, { headers });
      return data;
    } catch (error: unknown) {
      console.error("Error running crew:", error);

      const errorMessage = error instanceof Error
        ? error.message
        : "Unknown error";

      let errorCode = "unknown_error";
      let requestId = "unknown";
      let traceSnippet: string[] = [];

      if (error && typeof error === 'object' && 'response' in error &&
          error.response && typeof error.response === 'object' && 'data' in error.response) {
        const responseData = error.response.data as ErrorResponse;
        errorCode = responseData?.error?.code || errorCode;
        requestId = responseData?.error?.request_id || requestId;
        traceSnippet = responseData?.error?.trace_snippet || [];

        // Handle 401 Unauthorized errors specifically
        if ('status' in error.response && error.response.status === 401) {
          throw new Error(`Authentication failed: Invalid or missing API key`);
        }
      }

      const errorDetails = traceSnippet.length > 0
        ? `\nTrace: ${traceSnippet.join('\n')}`
        : '';

      throw new Error(`Agent workflow failed (${errorCode}): ${errorMessage} [Request ID: ${requestId}]${errorDetails}`);
    }
  }

  /**
   * Get agent information
   * @returns List of available agents
   */
  async getAgents(): Promise<Record<string, unknown>[]> {
    try {
      console.log('Fetching agents from:', `${API_BASE}/v1/agents`);
      const { data } = await api.get(`${API_BASE}/v1/agents`);
      return data;
    } catch (error: unknown) {
      console.error('Error fetching agents:', error);
      throw new Error(`Failed to fetch agents: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get health status of the API
   * @returns Health status
   */
  async getHealth(): Promise<{ status: string }> {
    try {
      console.log('Checking health at:', `${API_BASE}/v1/health`);
      const { data } = await api.get(`${API_BASE}/v1/health`);
      return data;
    } catch (error: unknown) {
      console.error('Error checking API health:', error);
      throw new Error(`API health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * AgenticSeek Chat - Emma's new AI agent system
   * @param message The message to send to Emma
   * @returns Emma's response with agent information
   */
  async agenticSeekChat(message: string): Promise<{
    response: string;
    agent_used?: string;
    thinking_process?: string;
    status: 'success' | 'error';
    error?: string;
  }> {
    try {
      if (!message) {
        throw new Error("Message is required for AgenticSeek chat");
      }

      const request = { message };

      console.log('Sending AgenticSeek request to:', `${API_BASE}/v1/agenticseek-browser/chat`);
      console.log('Request payload:', JSON.stringify(request, null, 2));

      const { data } = await api.post(`${API_BASE}/v1/agenticseek-browser/chat`, request, {
        timeout: 120000, // 2 minute timeout for AgenticSeek processing
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': `agenticseek_${Date.now()}`
        }
      });

      console.log('Received AgenticSeek response:', JSON.stringify(data, null, 2));

      if (!data || !data.response) {
        console.warn('Received empty or invalid response from AgenticSeek:', data);
        return {
          response: "Emma está procesando tu solicitud. Por favor intenta de nuevo en un momento.",
          status: 'error',
          error: "empty_response"
        };
      }

      return {
        response: data.response,
        agent_used: data.agent_used,
        thinking_process: data.thinking_process,
        status: 'success'
      };
    } catch (error: unknown) {
      console.error("Error in AgenticSeek chat:", error);

      const errorMessage = error instanceof Error ? error.message : "Unknown error";

      return {
        response: "Lo siento, Emma está experimentando dificultades técnicas. Por favor intenta de nuevo más tarde.",
        status: 'error',
        error: errorMessage
      };
    }
  }

  /**
   * Get AgenticSeek status
   * @returns Status of the AgenticSeek system
   */
  async getAgenticSeekStatus(): Promise<{
    status: string;
    agents_available: number;
    system_ready: boolean;
  }> {
    try {
      console.log('Checking AgenticSeek status at:', `${API_BASE}/v1/agenticseek/status`);
      const { data } = await api.get(`${API_BASE}/v1/agenticseek/status`);
      return data;
    } catch (error: unknown) {
      console.error('Error checking AgenticSeek status:', error);
      throw new Error(`AgenticSeek status check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get screenshot from AgenticSeek browser
   * @returns Screenshot blob
   */
  async getAgenticSeekScreenshot(): Promise<Blob | null> {
    try {
      console.log('Fetching AgenticSeek screenshot from:', `${API_BASE}/v1/agenticseek/screenshot`);
      const response = await api.get(`${API_BASE}/v1/agenticseek/screenshot`, {
        responseType: 'blob',
        timeout: 10000 // 10 second timeout
      });
      return response.data;
    } catch (error: unknown) {
      console.log('No screenshot available from AgenticSeek');
      return null;
    }
  }

  /**
   * Take a screenshot using AgenticSeek browser
   * @returns Success status
   */
  async takeAgenticSeekScreenshot(): Promise<{
    success: boolean;
    message: string;
    browser_type?: string;
  }> {
    try {
      console.log('Taking AgenticSeek screenshot at:', `${API_BASE}/v1/agenticseek/take-screenshot`);
      const { data } = await api.post(`${API_BASE}/v1/agenticseek/take-screenshot`, {}, {
        timeout: 60000 // 60 second timeout for screenshot
      });
      return data;
    } catch (error: unknown) {
      console.error('Error taking AgenticSeek screenshot:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Export a singleton instance
export const agentApiService = new AgentApiService();
export default agentApiService;
